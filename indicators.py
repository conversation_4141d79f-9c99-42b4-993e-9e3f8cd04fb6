from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import pandas_ta as ta
from hmmlearn.hmm import GaussianHMM
from sklearn.preprocessing import StandardScaler


def validate_ohlc_data(df: pd.DataFrame) -> tuple:
    """
    Validuje OHLC dáta a vr<PERSON>ti počet neplatných hodnôt.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        tuple: (invalid_open, invalid_high, invalid_low, invalid_close)
    """
    invalid_open = (df['open'] <= 0).sum()
    invalid_high = (df['high'] <= 0).sum()
    invalid_low = (df['low'] <= 0).sum()
    invalid_close = (df['close'] <= 0).sum()
    
    return invalid_open, invalid_high, invalid_low, invalid_close

def fix_ohlc_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Opraví neplatné OHLC dáta pomocou forward-fill a OHLC vztahov.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        DataFrame s opravenými dátami
    """
    df = df.copy()
    
    # Najprv forward-fill pre všetky stĺpce
    df['open'] = df['open'].replace(0, np.nan).ffill()
    df['high'] = df['high'].replace(0, np.nan).ffill()
    df['low'] = df['low'].replace(0, np.nan).ffill()
    df['close'] = df['close'].replace(0, np.nan).ffill()
    
    # Ak stále existujú NaN hodnoty na začiatku, použijeme backward-fill
    df['open'] = df['open'].bfill()
    df['high'] = df['high'].bfill()
    df['low'] = df['low'].bfill()
    df['close'] = df['close'].bfill()
    
    # Validácia OHLC vztahov
    # High musí byť >= max(open, close)
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    
    # Low musí byť <= min(open, close)
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    # Volume a count nesmú byť záporné
    if 'volume' in df.columns:
        df['volume'] = df['volume'].clip(lower=0)
    if 'count' in df.columns:
        df['count'] = df['count'].clip(lower=0)
    
    return df

def check_and_fix_data_quality(df: pd.DataFrame, file_path: str = "unknown") -> pd.DataFrame:
    """
    Skontroluje a opraví kvalitu OHLC dát.
    
    Args:
        df: DataFrame s OHLC dátami
        file_path: Cesta k súboru pre logovanie
        
    Returns:
        DataFrame s opravenými dátami
    """
    # Kontrola pred opravou
    invalid_before = validate_ohlc_data(df)
    
    if sum(invalid_before) > 0:
        _logger.warning(f"Nájdené neplatné OHLC hodnoty v {file_path}:")
        _logger.warning(f"  open: {invalid_before[0]} neplatných hodnôt")
        _logger.warning(f"  high: {invalid_before[1]} neplatných hodnôt")
        _logger.warning(f"  low: {invalid_before[2]} neplatných hodnôt")
        _logger.warning(f"  close: {invalid_before[3]} neplatných hodnôt")
        
        # Oprava dát
        df_fixed = fix_ohlc_data(df)
        
        # Kontrola po oprave
        invalid_after = validate_ohlc_data(df_fixed)
        
        if sum(invalid_after) == 0:
            _logger.info(f"✅ Všetky neplatné OHLC hodnoty boli opravené v {file_path}")
        else:
            _logger.warning(f"⚠️ Zostávajúce neplatné hodnoty v {file_path}:")
            _logger.warning(f"  open: {invalid_after[0]}, high: {invalid_after[1]}, low: {invalid_after[2]}, close: {invalid_after[3]}")
        
        return df_fixed
    else:
        _logger.debug(f"✅ Žiadne neplatné OHLC hodnoty v {file_path}")
        return df


_logger = logging.getLogger("Indicators")
_logger.setLevel(logging.INFO)


def _safe_idx(df: pd.DataFrame) -> pd.DatetimeIndex:
    idx = df.index if isinstance(df.index, pd.DatetimeIndex) else pd.to_datetime(df.index)
    return idx.tz_localize("UTC") if idx.tz is None else idx.tz_convert("UTC")

def _suffix(col: str, tf_src: str, prim_tf: str) -> str:
    return f"{col}_{tf_src}" if tf_src != prim_tf and not col.endswith(f"_{tf_src}") else col

def _ensure_cols(df: pd.DataFrame, cols: List[str]) -> None:
    for c in cols:
        if c not in df.columns:
            df[c] = np.nan

def _normalize_offset(off: str) -> str:
    return off[:-1] + "min" if isinstance(off, str) and off.endswith("m") else off


def _calc_order_flow(trades: pd.DataFrame, cfg: Dict[str, Any]) -> Tuple[pd.DataFrame, List[str]]:
    if trades.empty:
        return pd.DataFrame(index=trades.index), []
    df = trades.copy().sort_index()
    df["qty"] = pd.to_numeric(df["quantity"], errors="coerce")
    df["sign"] = np.where(df["is_buyer_maker"] == False, 1, -1)
    df["sv"] = df["qty"] * df["sign"]

    feat = pd.DataFrame(index=df.index)
    names: List[str] = []

    for p in cfg.get("volumeDeltaPeriods", []):
        col = f"volume_delta_{p}"
        try:
            if p[-1] in "smhd":
                feat[col] = df["sv"].rolling(_normalize_offset(p)).sum()
            else:
                ticks = int(p.rstrip("t"))
                feat[col] = df["sv"].rolling(ticks).sum()
            names.append(col)
        except Exception as e:
            _logger.error(f"Failed calculating {col}: {e}")

    for p in cfg.get("tradeCountDeltaPeriods", []):
        col = f"trade_count_delta_{p}"
        try:
            if p[-1] in "smhd":
                feat[col] = df["sign"].rolling(_normalize_offset(p)).sum()
            else:
                ticks = int(p.rstrip("t"))
                feat[col] = df["sign"].rolling(ticks).sum()
            names.append(col)
        except Exception as e:
             _logger.error(f"Failed calculating {col}: {e}")

    if cfg.get("cvdEnabled", False):
        freq = cfg.get("cvdResetFrequency", "daily")
        col = f"cvd_reset_{freq}"
        try:
            if freq == "daily":
                feat[col] = df.groupby(df.index.date)["sv"].cumsum()
            else:
                feat[col] = df["sv"].cumsum()
            names.append(col)
        except Exception as e:
             _logger.error(f"Failed calculating {col}: {e}")

    return feat.dropna(axis=1, how='all'), names


def _hmm_state(
    source: pd.DataFrame,
    hmm_cfg: Dict[str, Any],
    primary_tf: str,
    hmm_model_ext: Optional[GaussianHMM],
    hmm_scaler_ext: Optional[StandardScaler]
) -> Tuple[pd.Series, str]:
    n_comp = hmm_cfg.get("n_components", 3)
    tf_src = hmm_cfg.get("tf", primary_tf)
    src_type = hmm_cfg.get("data_source", "log_returns").lower()
    col_name = f"hmm_state_{n_comp}c_{src_type}_{tf_src}"

    if src_type == "log_returns":
        close = pd.to_numeric(source["close"], errors="coerce")
        feat_data = np.log(close / close.shift(1)).replace([np.inf, -np.inf], np.nan).dropna()
    elif src_type == "volatility":
         high = pd.to_numeric(source["high"], errors="coerce")
         low = pd.to_numeric(source["low"], errors="coerce")
         close = pd.to_numeric(source["close"], errors="coerce")
         atr = ta.atr(high, low, close, length=14, min_periods=14)
         feat_data = (atr / close).replace([np.inf, -np.inf], np.nan).dropna()
    else:
        _logger.warning(f"Unsupported HMM data_source: {src_type}. Returning NaNs.")
        return pd.Series(np.nan, index=source.index, name=col_name), col_name


    if feat_data.empty or feat_data.var() < 1e-10:
        _logger.warning(f"HMM feature '{src_type}' is empty or has near-zero variance. Returning NaNs.")
        return pd.Series(np.nan, index=source.index, name=col_name), col_name

    X = feat_data.to_numpy().reshape(-1, 1)

    try:
        scaler = hmm_scaler_ext or StandardScaler().fit(X)
        Xs = scaler.transform(X)

        model = hmm_model_ext
        if model is None or model.n_components != n_comp:
            model = GaussianHMM(
                n_components=n_comp,
                covariance_type="diag",
                n_iter=120,
                random_state=42
            ).fit(Xs)

        states = model.predict(Xs)
        s = pd.Series(states, index=feat_data.index, name=col_name, dtype="int8")
        return s.reindex(source.index).ffill().bfill(), col_name
    except Exception as e:
        _logger.error(f"HMM calculation failed for {col_name}: {e}", exc_info=True)
        return pd.Series(np.nan, index=source.index, name=col_name), col_name


def _hurst(series: pd.Series, lags: int = 20) -> float:
    series = series.dropna()
    if len(series) < lags + 5:
        return np.nan
    try:
        tau = [
            np.sqrt(((series.shift(-lag) - series) ** 2).mean())
            for lag in range(2, lags + 1)
        ]
        poly = np.polyfit(np.log(range(2, lags + 1)), np.log(tau), 1)
        return poly[0]
    except Exception as e:
        _logger.error(f"Hurst calculation failed: {e}")
        return np.nan


def _calc_ob_features(ob: pd.DataFrame, max_levels: int = 5) -> Tuple[pd.DataFrame, List[str]]:
    if ob.empty:
        _logger.warning("Order book DataFrame is empty, cannot calculate OB features.")
        return pd.DataFrame(index=ob.index), []

    ob = ob.copy()
    feat = pd.DataFrame(index=ob.index)
    calculated_names: List[str] = []
    required_cols = []
    for i in range(1, max_levels + 1):
        required_cols.extend([f'bid_px{i}', f'bid_qty{i}', f'ask_px{i}', f'ask_qty{i}'])

    missing_cols = [col for col in required_cols if col not in ob.columns]
    if missing_cols:
        _logger.warning(f"Missing required detailed order book columns: {missing_cols}. Cannot calculate all OB features.")
        return pd.DataFrame(index=ob.index), []

    try:
        feat["spread"] = ob["ask_px1"] - ob["bid_px1"]
        feat["mid_price"] = (ob["ask_px1"] + ob["bid_px1"]) / 2
        sum_qty1 = (ob["bid_qty1"] + ob["ask_qty1"]).replace(0, np.nan)
        feat["tob_imbalance"] = (ob["bid_qty1"] - ob["ask_qty1"]) / sum_qty1
        calculated_names.extend(["spread", "mid_price", "tob_imbalance"])

        cum_bid_qty_cols = [f'bid_qty{i}' for i in range(1, max_levels + 1)]
        cum_ask_qty_cols = [f'ask_qty{i}' for i in range(1, max_levels + 1)]
        cum_bid_qty5 = ob[cum_bid_qty_cols].sum(axis=1)
        cum_ask_qty5 = ob[cum_ask_qty_cols].sum(axis=1)
        sum_qty5 = (cum_bid_qty5 + cum_ask_qty5).replace(0, np.nan)
        feat["depth_imbalance5"] = (cum_bid_qty5 - cum_ask_qty5) / sum_qty5
        calculated_names.append("depth_imbalance5")

        for i in range(1, max_levels + 1):
             feat[f'ob_price_off_l{i}'] = ob[f'bid_px{i}'] - feat['mid_price']
             feat[f'ob_bid_vol_l{i}'] = ob[f'bid_qty{i}']
             feat[f'ob_ask_vol_l{i}'] = ob[f'ask_qty{i}']
             calculated_names.extend([f'ob_price_off_l{i}', f'ob_bid_vol_l{i}', f'ob_ask_vol_l{i}'])

        feat['dvol_bid_l1'] = ob['bid_qty1'].diff()
        feat['dvol_ask_l1'] = ob['ask_qty1'].diff()
        calculated_names.extend(['dvol_bid_l1', 'dvol_ask_l1'])

        levels = np.arange(1, max_levels + 1)
        log_levels = np.log(levels)
        slopes = []
        qty_cols = [f'bid_qty{i}' for i in levels]
        for _, row in ob[qty_cols].iterrows():
             vols = row.values.astype(float)
             valid_indices = (vols > 1e-9) & np.isfinite(vols)
             if valid_indices.sum() >= 2:
                 try:
                     log_vols_valid = np.log(vols[valid_indices])
                     log_levels_valid = log_levels[valid_indices]
                     if np.all(np.isfinite(log_vols_valid)):
                          poly = np.polyfit(log_levels_valid, log_vols_valid, 1)
                          slopes.append(poly[0])
                     else:
                          slopes.append(np.nan)
                 except (np.linalg.LinAlgError, ValueError):
                     slopes.append(np.nan)
             else:
                 slopes.append(np.nan)
        feat["depth_slope5"] = slopes
        calculated_names.append("depth_slope5")

    except Exception as e:
        _logger.error(f"Failed calculating OB features: {e}", exc_info=True)

    _logger.info(f"Calculated OB features: {len(calculated_names)}")
    return feat[calculated_names].dropna(axis=1, how='all'), calculated_names


def _calc_hf_trade_features(trades: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
    if trades.empty:
        _logger.warning("Trades DataFrame is empty, cannot calculate HF trade features.")
        return pd.DataFrame(index=trades.index), []

    hf_feat = pd.DataFrame(index=trades.index)
    calculated_names: List[str] = []

    try:
        hf_feat['last_trade_dt'] = trades.index.to_series().diff().dt.total_seconds()
        calculated_names.append('last_trade_dt')

        trades['direction'] = np.where(trades['is_buyer_maker'] == False, 1, -1)
        hf_feat['trade_dir_sum_1s'] = trades['direction'].rolling('1s', closed='left').sum()
        calculated_names.append('trade_dir_sum_1s')

        trades['quantity_num'] = pd.to_numeric(trades['quantity'], errors='coerce')
        hf_feat['trade_skew_1s'] = trades['quantity_num'].rolling('1s', closed='left').skew()
        calculated_names.append('trade_skew_1s')

    except Exception as e:
        _logger.error(f"Failed calculating HF trade features: {e}", exc_info=True)

    _logger.info(f"Calculated HF Trade features: {len(calculated_names)}")
    return hf_feat[calculated_names].dropna(axis=1, how='all'), calculated_names


def calculate_and_merge_indicators(
    data: Dict[str, pd.DataFrame],
    cfg: Dict[str, Any],
    *,
    skip_hmm: bool = False,
    hmm_model_external: Optional[GaussianHMM] = None,
    hmm_scaler_external: Optional[StandardScaler] = None,
) -> Tuple[pd.DataFrame, List[str]]:
    prim_tf = cfg["primaryTimeframe"]
    
    # 🚨 CRITICAL FIX: Use 5m data for indicators when available
    # This fixes the issue where 1s primary timeframe was using 1s data for indicators
    # but indicators need 5m data for proper calculation
    indicator_tf = "5m" if "5m" in data and not data["5m"].empty else prim_tf
    
    if indicator_tf not in data or data[indicator_tf].empty:
        _logger.error(f"Indicator timeframe '{indicator_tf}' not found in input data or is empty.")
        return pd.DataFrame(), []
        
    _logger.info(f"🔧 Using timeframe '{indicator_tf}' for indicator calculations (primary: {prim_tf})")

    for tf, df in data.items():
        # only operate on actual DataFrames
        if not isinstance(df, pd.DataFrame):
            _logger.debug(f"Skipping non-DataFrame entry for '{tf}'")
            continue
        if df.empty:
            _logger.debug(f"DataFrame for '{tf}' is empty, skipping")
            continue
        # now you know df is a non-empty DataFrame
        if not isinstance(df.index, pd.DatetimeIndex):
            try:
                df.index = pd.to_datetime(df.index)
                _logger.debug(f"Converted index of '{tf}' to DatetimeIndex.")
            except Exception as e:
                _logger.warning(f"Could not convert index of '{tf}' to DatetimeIndex: {e}. Skipping timezone handling for it.")
                continue

        if isinstance(df.index, pd.DatetimeIndex):
            df.index = _safe_idx(df)
            
            # 🚨 CRITICAL FIX: Remove duplicate timestamps from ALL timeframes
            if df.index.duplicated().any():
                duplicate_count = df.index.duplicated().sum()
                _logger.warning(f"🔍 DUPLICATE TIMESTAMPS DETECTED in timeframe '{tf}': {duplicate_count} duplicates")
                _logger.warning(f"   Index before dedup: {len(df)} rows")
                data[tf] = df[~df.index.duplicated(keep='last')]  # Keep last occurrence and update in data dict
                _logger.warning(f"   Index after dedup: {len(data[tf])} rows")
            
            if not data[tf].index.is_monotonic_increasing:
                _logger.debug(f"Sorting index for timeframe '{tf}'.")
                data[tf].sort_index(inplace=True)
        else:
            _logger.warning(f"Index of '{tf}' is not DatetimeIndex after conversion attempt, cannot ensure UTC or sort.")

    base = data[indicator_tf].copy()
    base.index.name = "timestamp"
    if not base.index.is_monotonic_increasing:
         _logger.info(f"Sorting index for indicator timeframe '{indicator_tf}'.")
         base = base.sort_index()

    # 🚨 CRITICAL FIX: Remove duplicate timestamps from base data
    if base.index.duplicated().any():
        duplicate_count = base.index.duplicated().sum()
        _logger.warning(f"🔍 DUPLICATE TIMESTAMPS DETECTED in indicator timeframe '{indicator_tf}': {duplicate_count} duplicates")
        _logger.warning(f"   Index before dedup: {len(base)} rows")
        base = base[~base.index.duplicated(keep='last')]  # Keep last occurrence
        _logger.warning(f"   Index after dedup: {len(base)} rows")

    # Apply data quality fix to base data before indicator calculation
    _logger.info("Checking and fixing data quality for indicator timeframe...")
    base = check_and_fix_data_quality(base, f"indicator_timeframe_{indicator_tf}")

    # CRITICAL DEBUG: Check base data before indicator calculation
    _logger.info(f"🔍 DEBUG: Base data for indicator calculation:")
    _logger.info(f"   Timeframe: {indicator_tf}")
    _logger.info(f"   Rows: {len(base)}")
    _logger.info(f"   Columns: {list(base.columns)}")
    if len(base) > 0:
        _logger.info(f"   Date range: {base.index[0]} to {base.index[-1]}")
        if 'close' in base.columns:
            close_vals = base['close'].dropna()
            if len(close_vals) > 0:
                _logger.info(f"   Close price range: {close_vals.min():.6f} to {close_vals.max():.6f}")
                _logger.info(f"   Latest close: {close_vals.iloc[-1]:.6f}")
                _logger.info(f"   Non-null close values: {len(close_vals)}/{len(base)}")
            else:
                _logger.error(f"   ❌ All close values are NaN!")
        else:
            _logger.error(f"   ❌ No 'close' column in base data!")
    else:
        _logger.error(f"   ❌ Base data is empty!")
        return pd.DataFrame(), []

    all_calculated_feature_names: List[str] = []

    _logger.info("Calculating technical indicators...")
    for name, s in cfg.get("indicatorSettings", {}).items():
        if not isinstance(s, dict) or not s.get("enabled", True):
            continue
        key = name.lower()
        tf_src = s.get("tf", prim_tf)
        if tf_src not in data or data[tf_src].empty:
            _logger.warning(f"Source timeframe '{tf_src}' for indicator '{name}' not found or empty. Skipping.")
            continue
        src = data[tf_src]
        src_h = pd.to_numeric(src["high"], errors='coerce')
        src_l = pd.to_numeric(src["low"], errors='coerce')
        src_c = pd.to_numeric(src["close"], errors='coerce')

        try:
            if key.startswith("ema"):
                periods = {s.get("baseFastPeriod"), s.get("baseSlowPeriod"), s.get("basePeriod")}
                for p in {p_ for p_ in periods if p_ is not None and isinstance(p_, int) and p_ > 0}:
                    dst = _suffix(f"EMA_{p}", tf_src, prim_tf)
                    _logger.info(f"🔧 Calculating EMA_{p} from {len(src_c)} close values")
                    _logger.info(f"   Source close range: {src_c.min():.6f} to {src_c.max():.6f}")
                    _logger.info(f"   Non-null values: {src_c.count()}/{len(src_c)}")

                    ema_result = ta.ema(src_c, length=p, min_periods=p)
                    base[dst] = ema_result

                    # DEBUG: Check if EMA was properly added to base
                    if dst in base.columns:
                        valid_values = base[dst].dropna()
                        finite_values = base[dst][np.isfinite(base[dst])]
                        _logger.info(f"   ✅ {dst} added to base: {len(finite_values)} finite values, {len(valid_values)} valid values")
                        if len(finite_values) > 0:
                            _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")
                    else:
                        _logger.error(f"   ❌ {dst} NOT found in base after assignment!")

                    if ema_result is not None and not ema_result.empty:
                        valid_ema = ema_result.dropna()
                        if len(valid_ema) > 0:
                            _logger.info(f"   ✅ EMA_{p} calculated: {len(valid_ema)} valid values, latest: {valid_ema.iloc[-1]:.6f}")
                        else:
                            _logger.warning(f"   ⚠️ EMA_{p} calculated but all values are NaN")
                    else:
                        _logger.error(f"   ❌ EMA_{p} calculation failed - returned None or empty")

                    all_calculated_feature_names.append(dst)

            elif key.startswith("rsi") and not key.startswith("stochrsi"):
                p = s.get("basePeriod", 14)
                dst = _suffix(f"RSI_{p}", tf_src, prim_tf)
                _logger.info(f"🔧 Calculating RSI_{p} from {len(src_c)} close values")
                _logger.info(f"   Source close range: {src_c.min():.6f} to {src_c.max():.6f}")
                _logger.info(f"   Non-null values: {src_c.count()}/{len(src_c)}")

                rsi_result = ta.rsi(src_c, length=p, min_periods=p)
                base[dst] = rsi_result

                # DEBUG: Check if RSI was properly added to base
                if dst in base.columns:
                    valid_values = base[dst].dropna()
                    finite_values = base[dst][np.isfinite(base[dst])]
                    _logger.info(f"   ✅ {dst} added to base: {len(finite_values)} finite values, {len(valid_values)} valid values")
                    if len(finite_values) > 0:
                        _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")
                else:
                    _logger.error(f"   ❌ {dst} NOT found in base after assignment!")

                if rsi_result is not None and not rsi_result.empty:
                    valid_rsi = rsi_result.dropna()
                    if len(valid_rsi) > 0:
                        _logger.info(f"   ✅ RSI_{p} calculated: {len(valid_rsi)} valid values, latest: {valid_rsi.iloc[-1]:.6f}")
                    else:
                        _logger.warning(f"   ⚠️ RSI_{p} calculated but all values are NaN")
                else:
                    _logger.error(f"   ❌ RSI_{p} calculation failed - returned None or empty")

                all_calculated_feature_names.append(dst)

            elif key.startswith("atr"):
                p = s.get("basePeriod", 14)
                dst = _suffix(f"ATR_{p}", tf_src, prim_tf)
                _logger.info(f"🔧 Calculating ATR_{p} from {len(src_h)} OHLC values")
                _logger.info(f"   High range: {src_h.min():.6f} to {src_h.max():.6f}")
                _logger.info(f"   Low range: {src_l.min():.6f} to {src_l.max():.6f}")
                _logger.info(f"   Close range: {src_c.min():.6f} to {src_c.max():.6f}")
                _logger.info(f"   Non-null H/L/C: {src_h.count()}/{src_l.count()}/{src_c.count()}")

                atr_result = ta.atr(src_h, src_l, src_c, length=p, min_periods=p)
                base[dst] = atr_result

                # DEBUG: Check if ATR was properly added to base
                if dst in base.columns:
                    valid_values = base[dst].dropna()
                    finite_values = base[dst][np.isfinite(base[dst])]
                    _logger.info(f"   ✅ {dst} added to base: {len(finite_values)} finite values, {len(valid_values)} valid values")
                    if len(finite_values) > 0:
                        _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")
                else:
                    _logger.error(f"   ❌ {dst} NOT found in base after assignment!")

                if atr_result is not None and not atr_result.empty:
                    valid_atr = atr_result.dropna()
                    if len(valid_atr) > 0:
                        _logger.info(f"   ✅ ATR_{p} calculated: {len(valid_atr)} valid values, latest: {valid_atr.iloc[-1]:.6f}")
                    else:
                        _logger.warning(f"   ⚠️ ATR_{p} calculated but all values are NaN")
                else:
                    _logger.error(f"   ❌ ATR_{p} calculation failed - returned None or empty")

                all_calculated_feature_names.append(dst)

            elif key.startswith("vwap"):
                dst = _suffix("VWAP_pta", tf_src, prim_tf)
                trades_df = data.get("trades")
                if trades_df is not None and not trades_df.empty:
                    df_tr = trades_df.copy()
                    if not isinstance(df_tr.index, pd.DatetimeIndex):
                        df_tr.index = pd.to_datetime(df_tr.index)
                    if df_tr.index.tz != base.index.tz:
                        df_tr.index = _safe_idx(df_tr).tz_convert(base.index.tz)
                    if not df_tr.index.is_monotonic_increasing: df_tr.sort_index(inplace=True)

                    df_tr['price_num'] = pd.to_numeric(df_tr['price'], errors='coerce')
                    df_tr['quantity_num'] = pd.to_numeric(df_tr['quantity'], errors='coerce')
                    df_tr = df_tr.dropna(subset=['price_num', 'quantity_num'])

                    freq = _normalize_offset(prim_tf)
                    df_tr["bar"] = df_tr.index.floor(freq)

                    vwap_s = (
                        df_tr[df_tr['quantity_num'] > 0]
                        .groupby("bar")
                        .apply(lambda x: np.average(x['price_num'], weights=x['quantity_num']))
                        .rename(dst)
                    )
                    if not base.index.is_monotonic_increasing: base.sort_index(inplace=True)
                    base[dst] = vwap_s.reindex(base.index).ffill()
                else:
                    base[dst] = np.nan
                all_calculated_feature_names.append(dst)

            elif key.startswith("bollinger"):
                p = s.get("basePeriod", 20)
                sd = s.get("baseStDev", 2.0)
                bb = ta.bbands(src_c, length=p, std=sd, min_periods=p)
                if bb is not None and not bb.empty:
                    mapping = {"L": "lower", "M": "middle", "U": "upper"}
                    for sub in ["L", "M", "U"]:
                        col = f"BBL_{p}_{sd}" if sub == "L" else f"BBM_{p}_{sd}" if sub == "M" else f"BBU_{p}_{sd}"
                        if col in bb.columns:
                            dst = _suffix(f"bollinger_bands_{mapping[sub]}_{p}_{sd}", tf_src, prim_tf)
                            base[dst] = bb[col]
                            all_calculated_feature_names.append(dst)

                    bbw_col = f"BBB_{p}_{sd}"
                    bbp_col = f"BBP_{p}_{sd}"
                    dst_w = _suffix(f"bollinger_bands_width_{p}_{sd}", tf_src, prim_tf)
                    if bbw_col in bb.columns:
                         up_col = _suffix(f"bollinger_bands_upper_{p}_{sd}", tf_src, prim_tf)
                         low_col = _suffix(f"bollinger_bands_lower_{p}_{sd}", tf_src, prim_tf)
                         mid_col = _suffix(f"bollinger_bands_middle_{p}_{sd}", tf_src, prim_tf)
                         if all(c in base.columns for c in [up_col, low_col, mid_col]):
                              base[dst_w] = (base[up_col] - base[low_col]) / base[mid_col].replace(0, np.nan)
                         else:
                             base[dst_w] = np.nan
                    else:
                         base[dst_w] = np.nan
                    all_calculated_feature_names.append(dst_w)


            elif key.startswith("adx"):
                p = s.get("basePeriod", 14)
                _logger.info(f"🔧 Calculating ADX_{p} from {len(src_h)} OHLC values")
                _logger.info(f"   High range: {src_h.min():.6f} to {src_h.max():.6f}")
                _logger.info(f"   Low range: {src_l.min():.6f} to {src_l.max():.6f}")
                _logger.info(f"   Close range: {src_c.min():.6f} to {src_c.max():.6f}")
                _logger.info(f"   Non-null H/L/C: {src_h.count()}/{src_l.count()}/{src_c.count()}")

                adx = ta.adx(src_h, src_l, src_c, length=p, min_periods=p * 2)
                if adx is not None and not adx.empty:
                    _logger.info(f"   ADX calculation returned {len(adx)} rows with columns: {list(adx.columns)}")
                    for sub in ["ADX", "DMP", "DMN"]:
                        col = f"{sub}_{p}"
                        if col in adx.columns:
                            dst = _suffix(col, tf_src, prim_tf)
                            base[dst] = adx[col]

                            valid_values = adx[col].dropna()
                            if len(valid_values) > 0:
                                _logger.info(f"   ✅ {sub}_{p} calculated: {len(valid_values)} valid values, latest: {valid_values.iloc[-1]:.6f}")
                            else:
                                _logger.warning(f"   ⚠️ {sub}_{p} calculated but all values are NaN")

                            all_calculated_feature_names.append(dst)
                        else:
                            _logger.warning(f"   ⚠️ Column {col} not found in ADX result")
                else:
                    _logger.error(f"   ❌ ADX calculation failed - returned None or empty")

            elif key.startswith("macd"):
                f_per = s.get("fastPeriod", 12)
                s_per = s.get("slowPeriod", 26)
                sig = s.get("signalPeriod", 9)
                min_p = s_per + sig - 1
                macd = ta.macd(src_c, fast=f_per, slow=s_per, signal=sig, min_periods=min_p)
                if macd is not None and not macd.empty:
                    mapping_macd = {
                        f"MACD_{f_per}_{s_per}_{sig}": "MACD_line",
                        f"MACDh_{f_per}_{s_per}_{sig}": "MACD_hist",
                        f"MACDs_{f_per}_{s_per}_{sig}": "MACD_signal",
                    }
                    for src_col, alias in mapping_macd.items():
                        if src_col in macd.columns:
                            dst = _suffix(f"{alias}_{f_per}_{s_per}_{sig}", tf_src, prim_tf)
                            base[dst] = macd[src_col]
                            all_calculated_feature_names.append(dst)

            elif key.startswith("stochrsi"):
                rsi_len = s.get("rsiPeriod", 14)
                st_len = s.get("stochPeriod", 14)
                k_sm = s.get("smoothK", 3)
                d_sm = s.get("smoothD", 3)
                min_p = rsi_len + st_len + max(k_sm, d_sm)
                st = ta.stochrsi(
                    src_c,
                    rsi_length=rsi_len,
                    stoch_length=st_len,
                    k=k_sm,
                    d=d_sm,
                    min_periods=min_p,
                )
                if st is not None and not st.empty:
                    for src_col, alias in [
                        (f"STOCHRSIk_{rsi_len}_{st_len}_{k_sm}_{d_sm}", "stoch_rsi_k"),
                        (f"STOCHRSId_{rsi_len}_{st_len}_{k_sm}_{d_sm}", "stoch_rsi_d"),
                    ]:
                        if src_col in st.columns:
                            dst = _suffix(f"{alias}_{rsi_len}_{st_len}_{k_sm}_{d_sm}", tf_src, prim_tf)
                            base[dst] = st[src_col]
                            all_calculated_feature_names.append(dst)

            elif key.startswith("kama"):
                per = s.get("period", 10)
                kama = ta.kama(src_c, length=per, min_periods=per)
                if kama is not None:
                    dst = _suffix(f"KAMA_{per}", tf_src, prim_tf)
                    base[dst] = kama
                    all_calculated_feature_names.append(dst)

        except Exception as e:
            _logger.error(f"Failed calculating indicator {name} ({key}): {e}", exc_info=True)


    if not skip_hmm:
        _logger.info("Calculating HMM state...")
        hmm_cfg = cfg.get("hmmSettings", {})
        if hmm_cfg and hmm_cfg.get("enabled", True):
            try:
                hmm_tf = hmm_cfg.get("tf", prim_tf)
                if hmm_tf in data and not data[hmm_tf].empty:
                     hmm_source_df = data[hmm_tf]
                     s, col = _hmm_state(hmm_source_df, hmm_cfg, prim_tf, hmm_model_external, hmm_scaler_external)
                     base[col] = s
                     all_calculated_feature_names.append(col)
                else:
                     _logger.warning(f"HMM source timeframe '{hmm_tf}' not found or empty. Skipping HMM.")
            except Exception as e:
                 _logger.error(f"Failed calculating HMM state: {e}", exc_info=True)

    _logger.info("Calculating Volume Imbalance...")
    vi = cfg.get("indicatorSettings", {}).get("volumeImbalance_5m", {})
    if vi.get("enabled", True):
        tf_vi = vi.get("tf", prim_tf)
        if tf_vi in data and not data[tf_vi].empty and all(c in data[tf_vi].columns for c in ["buy_volume", "sell_volume", "volume"]):
            try:
                dst = f"volume_imbalance_{tf_vi}"
                src_vi = data[tf_vi]
                vol = pd.to_numeric(src_vi["volume"], errors='coerce').replace(0, np.nan)
                buy_vol = pd.to_numeric(src_vi["buy_volume"], errors='coerce')
                sell_vol = pd.to_numeric(src_vi["sell_volume"], errors='coerce')
                base[dst] = (buy_vol - sell_vol) / vol
                all_calculated_feature_names.append(dst)
            except Exception as e:
                _logger.error(f"Failed calculating Volume Imbalance: {e}", exc_info=True)
        else:
             _logger.warning(f"Volume Imbalance source timeframe '{tf_vi}' missing, empty or lacks required columns.")


    _logger.info("Calculating and merging order flow features...")
    of_cfg = cfg.get("indicatorSettings", {}).get("orderFlowFeatures", {})
    trades_data = data.get("trades")
    if of_cfg.get("enabled", False) and trades_data is not None and not trades_data.empty:
        try:
            of_df, of_cols = _calc_order_flow(trades_data, of_cfg)
            if not of_df.empty and of_cols:
                colliding_cols = [c for c in of_cols if c in base.columns and c in of_df.columns]
                if colliding_cols:
                    _logger.warning(f"Order flow columns {colliding_cols} exist in base DataFrame. Dropping them before merge.")
                    base.drop(columns=colliding_cols, inplace=True, errors='ignore')

                cols_to_merge = [c for c in of_cols if c in of_df.columns]

                if cols_to_merge:
                    _logger.info(f"Merging order flow features: {cols_to_merge}")
                    if not isinstance(of_df.index, pd.DatetimeIndex): of_df.index = pd.to_datetime(of_df.index)
                    if of_df.index.tz != base.index.tz:
                         of_df.index = _safe_idx(of_df).tz_convert(base.index.tz)
                    if not base.index.is_monotonic_increasing: base.sort_index(inplace=True)
                    if not of_df.index.is_monotonic_increasing: of_df.sort_index(inplace=True)

                    base = pd.merge_asof(
                        base, of_df[cols_to_merge],
                        left_index=True, right_index=True,
                        direction="backward"
                    )
                    all_calculated_feature_names.extend(cols_to_merge)
                else:
                    _logger.info("No new or valid order flow columns to merge.")
            else:
                 _logger.warning("`_calc_order_flow` returned empty DataFrame or no columns.")
        except Exception as e:
            _logger.error(f"Failed during order flow calculation or merge: {e}", exc_info=True)
    elif of_cfg.get("enabled", False):
        _logger.warning("Order flow features enabled in config, but 'trades' data is missing or empty.")

    _logger.info("Calculating and merging High-Frequency trade features...")
    hf_trade_df = pd.DataFrame(index=base.index) # Initialize empty DF aligned with base index
    hf_trade_cols = []
    if trades_data is not None and not trades_data.empty:
        try:
            hf_feat_df_raw, hf_feat_cols_raw = _calc_hf_trade_features(trades_data)
            if not hf_feat_df_raw.empty and hf_feat_cols_raw:
                cols_to_merge = [c for c in hf_feat_cols_raw if c in hf_feat_df_raw.columns]
                if cols_to_merge:
                     _logger.info(f"Merging HF Trade features: {cols_to_merge}")
                     if not isinstance(hf_feat_df_raw.index, pd.DatetimeIndex): hf_feat_df_raw.index = pd.to_datetime(hf_feat_df_raw.index)
                     if hf_feat_df_raw.index.tz != base.index.tz:
                          hf_feat_df_raw.index = _safe_idx(hf_feat_df_raw).tz_convert(base.index.tz)
                     if not base.index.is_monotonic_increasing: base.sort_index(inplace=True)
                     if not hf_feat_df_raw.index.is_monotonic_increasing: hf_feat_df_raw.sort_index(inplace=True)

                     colliding_cols = [c for c in cols_to_merge if c in base.columns]
                     if colliding_cols:
                         _logger.warning(f"HF Trade columns {colliding_cols} exist in base DataFrame. Dropping them before merge.")
                         base.drop(columns=colliding_cols, inplace=True, errors='ignore')

                     base = pd.merge_asof(
                         base, hf_feat_df_raw[cols_to_merge],
                         left_index=True, right_index=True,
                         direction='backward',
                         tolerance=pd.Timedelta(seconds=10) # Allow slight mismatch for backward fill
                     )
                     all_calculated_feature_names.extend(cols_to_merge)
                     # Optionally forward fill HF features after merge?
                     # base[cols_to_merge] = base[cols_to_merge].ffill()
                else:
                     _logger.info("No new or valid HF trade columns to merge.")
            else:
                _logger.warning("`_calc_hf_trade_features` returned empty DataFrame or no columns.")
        except Exception as e:
             _logger.error(f"Failed during HF trade feature calculation or merge: {e}", exc_info=True)


    _logger.info("Calculating and merging order book features...")
    ob_data = data.get("orderbooks")
    if ob_data is not None and not ob_data.empty:
        try:
            ob_df, ob_cols = _calc_ob_features(ob_data)
            if not ob_df.empty and ob_cols:
                colliding_cols = [c for c in ob_cols if c in base.columns and c in ob_df.columns]
                if colliding_cols:
                    _logger.warning(f"Order book columns {colliding_cols} exist in base DataFrame. Dropping them before merge.")
                    base.drop(columns=colliding_cols, inplace=True, errors='ignore')

                cols_to_merge = [c for c in ob_cols if c in ob_df.columns]

                if cols_to_merge:
                    _logger.info(f"Merging order book features: {cols_to_merge}")
                    if not isinstance(ob_df.index, pd.DatetimeIndex): ob_df.index = pd.to_datetime(ob_df.index)
                    if ob_df.index.tz != base.index.tz:
                         ob_df.index = _safe_idx(ob_df).tz_convert(base.index.tz)
                    if not base.index.is_monotonic_increasing: base.sort_index(inplace=True)
                    if not ob_df.index.is_monotonic_increasing: ob_df.sort_index(inplace=True)

                    base = pd.merge_asof(
                        base,
                        ob_df[cols_to_merge],
                        left_index=True,
                        right_index=True,
                        direction="nearest",
                        tolerance=pd.Timedelta(seconds=15)
                    )

                    try:
                        actual_merged_cols = [c for c in cols_to_merge if c in base.columns]
                        if actual_merged_cols:
                             base[actual_merged_cols] = base[actual_merged_cols].ffill().bfill()
                             all_calculated_feature_names.extend(actual_merged_cols)
                             _logger.info(f"Successfully filled NaNs for merged OB columns: {actual_merged_cols}")
                        else:
                             _logger.warning(f"Columns {cols_to_merge} were expected but not found in base DataFrame after OB merge_asof.")

                    except KeyError as ke:
                         _logger.error(f"KeyError during ffill/bfill after OB merge, even after checks. Merge might have failed unexpectedly. Error: {ke}", exc_info=True)
                    except Exception as e_fill:
                         _logger.error(f"Error during ffill/bfill after OB merge: {e_fill}", exc_info=True)
                else:
                    _logger.info("No new or valid order book columns to merge.")

            else:
                _logger.warning("`_calc_ob_features` returned empty DataFrame or no columns.")
        except Exception as e:
            _logger.error(f"Failed during order book calculation or merge: {e}", exc_info=True)
    else:
        _logger.warning("Order book data ('orderbooks') is missing or empty. Skipping OB features.")


    # --- Calculate time-since-event features ---
    _logger.info("Calculating time-since-event features...")
    feature_cols = cfg.get("envSettings", {}).get("feature_columns", [])
    
    # Initialize time-since features if they're in the config
    if "dt_since_buy" in feature_cols or "dt_since_sell" in feature_cols:
        try:
            # These features track time since last buy/sell signals
            # For simulation, we'll initialize them based on price momentum
            
            close_prices = pd.to_numeric(base["close"], errors='coerce')
            price_change = close_prices.pct_change()
            
            # Simple logic: "buy signal" = strong positive momentum, "sell signal" = strong negative momentum
            buy_threshold = price_change.quantile(0.8)  # Top 20% of price moves
            sell_threshold = price_change.quantile(0.2)  # Bottom 20% of price moves
            
            buy_signals = price_change > buy_threshold
            sell_signals = price_change < sell_threshold
            
            if "dt_since_buy" in feature_cols:
                # Calculate seconds since last buy signal
                buy_indices = buy_signals[buy_signals].index
                dt_since_buy = []
                last_buy_time = None
                
                for timestamp in base.index:
                    if timestamp in buy_indices:
                        last_buy_time = timestamp
                        dt_since_buy.append(0.0)
                    elif last_buy_time is not None:
                        time_diff = (timestamp - last_buy_time).total_seconds()
                        dt_since_buy.append(min(time_diff, 3600.0))  # Cap at 1 hour
                    else:
                        dt_since_buy.append(3600.0)  # Default to 1 hour if no previous signal
                
                base["dt_since_buy"] = dt_since_buy
                all_calculated_feature_names.append("dt_since_buy")
                
            if "dt_since_sell" in feature_cols:
                # Calculate seconds since last sell signal
                sell_indices = sell_signals[sell_signals].index
                dt_since_sell = []
                last_sell_time = None
                
                for timestamp in base.index:
                    if timestamp in sell_indices:
                        last_sell_time = timestamp
                        dt_since_sell.append(0.0)
                    elif last_sell_time is not None:
                        time_diff = (timestamp - last_sell_time).total_seconds()
                        dt_since_sell.append(min(time_diff, 3600.0))  # Cap at 1 hour
                    else:
                        dt_since_sell.append(3600.0)  # Default to 1 hour if no previous signal
                
                base["dt_since_sell"] = dt_since_sell
                all_calculated_feature_names.append("dt_since_sell")
                
            _logger.info("Successfully calculated time-since-event features")
            
        except Exception as e:
            _logger.error(f"Failed calculating time-since-event features: {e}", exc_info=True)
            # Fallback to zeros if calculation fails
            if "dt_since_buy" in feature_cols:
                base["dt_since_buy"] = 0.0
            if "dt_since_sell" in feature_cols:
                base["dt_since_sell"] = 0.0

    _logger.info("Performing final cleanup...")
    # CRITICAL FIX: Create a copy to prevent modifying the original config
    final_expected_columns = list(cfg.get("envSettings", {}).get("feature_columns", []))
    if not final_expected_columns:
         _logger.warning("No 'feature_columns' defined in 'envSettings'. Using all calculated columns.")
         final_expected_columns = list(base.columns)
    else:
         essential_base = ['open', 'high', 'low', 'close', 'volume']
         for col in essential_base:
              if col not in final_expected_columns and col in base.columns:
                   final_expected_columns.insert(essential_base.index(col), col)
         for col in base.columns:
              if col not in final_expected_columns:
                   final_expected_columns.append(col)


    _ensure_cols(base, final_expected_columns)

    # DEBUG: Check indicator values BEFORE replace inf with nan
    indicator_features = ['ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14']
    _logger.info("🔍 DEBUG: Indicator values BEFORE replace([inf, -inf], nan):")
    for col in indicator_features:
        if col in base.columns:
            values = base[col]
            valid_values = values.dropna()
            finite_values = values[np.isfinite(values)]
            _logger.info(f"   {col}: {len(finite_values)} finite values, {len(valid_values)} valid values, {np.isinf(values).sum()} inf values")
            if len(finite_values) > 0:
                _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")

    base.replace([np.inf, -np.inf], np.nan, inplace=True)

    # DEBUG: Check indicator values AFTER replace inf with nan
    _logger.info("🔍 DEBUG: Indicator values AFTER replace([inf, -inf], nan):")
    for col in indicator_features:
        if col in base.columns:
            values = base[col]
            valid_values = values.dropna()
            finite_values = values[np.isfinite(values)]
            _logger.info(f"   {col}: {len(finite_values)} finite values, {len(valid_values)} valid values")
            if len(finite_values) > 0:
                _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")

    ta_indicators_calculated = [
        col for col in all_calculated_feature_names if
        any(prefix in col for prefix in ["EMA_", "RSI_", "ATR_", "VWAP_", "bollinger_", "ADX_", "DMP_", "DMN_", "MACD_", "stoch_rsi_", "KAMA_"])
    ]
    if ta_indicators_calculated:
         cols_to_ffill = [col for col in ta_indicators_calculated if col in base.columns]
         if cols_to_ffill:
              _logger.info(f"🔍 DEBUG: Forward-filling TA indicators: {cols_to_ffill}")
              base[cols_to_ffill] = base[cols_to_ffill].ffill()

    # DEBUG: Check indicator values AFTER forward fill
    _logger.info("🔍 DEBUG: Indicator values AFTER forward fill:")
    for col in indicator_features:
        if col in base.columns:
            values = base[col]
            valid_values = values.dropna()
            finite_values = values[np.isfinite(values)]
            _logger.info(f"   {col}: {len(finite_values)} finite values, {len(valid_values)} valid values")
            if len(finite_values) > 0:
                _logger.info(f"      Latest finite: {finite_values.iloc[-1]:.6f}")


    base = base.dropna(subset=["open", "high", "low", "close"], how='any')

    for col in base.select_dtypes(include=np.number).columns:
         if base[col].isnull().sum() < len(base) * 0.95:
             try:
                 if pd.api.types.is_float_dtype(base[col]) or pd.api.types.is_integer_dtype(base[col]):
                      base[col] = pd.to_numeric(base[col], errors='coerce').astype("float32")

             except Exception as e:
                  _logger.warning(f"Could not convert column '{col}' to float32: {e}")


    output_columns_ordered = []
    existing_columns = set(base.columns)
    config_feature_cols = cfg.get("envSettings", {}).get("feature_columns", [])

    for col in config_feature_cols:
        if col in existing_columns:
            output_columns_ordered.append(col)
            existing_columns.remove(col)

    output_columns_ordered.extend(sorted(list(existing_columns)))

    final_df = base[[col for col in output_columns_ordered if col in base.columns]]

    _logger.info(f"Final DataFrame shape: {final_df.shape}. Columns: {list(final_df.columns)}")
    return final_df, list(final_df.columns)